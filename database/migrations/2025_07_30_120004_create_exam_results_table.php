<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_results', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->foreignUuid('exam_question_id')
                ->constrained('exam_questions')
                ->cascadeOnDelete();

            // Rating fields matching the existing Answer model and Settings
            $table->unsignedTinyInteger('memorization')->nullable(); // الحفظ
            $table->unsignedTinyInteger('tajweed_rules')->nullable(); // الأحكام
            $table->unsignedTinyInteger('narration_principles')->nullable(); // أصل الرواية
            $table->unsignedTinyInteger('stopping_starting_rules')->nullable(); // الوقف والبداية
            $table->unsignedTinyInteger('recitation_performance')->nullable(); // الأداء
            $table->unsignedTinyInteger('sound')->nullable(); // الصوت

            $table->decimal('total_score', 5, 2)->nullable(); // Calculated total score
            $table->timestamps();

            // Ensure unique result per exam question
            $table->unique(['exam_question_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_results');
    }
};
