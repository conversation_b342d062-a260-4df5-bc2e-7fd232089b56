<?php

declare(strict_types=1);

use App\Http\Controllers\Api\ExamController;
use App\Http\Controllers\Api\ExamQuestionController;
use App\Http\Controllers\Api\ExamResultController;
use App\Http\Controllers\Api\ExamStudentController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Exam API Routes
Route::prefix('exams')->group(function () {
    // Exam CRUD
    Route::get('/', [ExamController::class, 'index']);
    Route::post('/', [ExamController::class, 'store']);
    Route::get('/{exam}', [ExamController::class, 'show']);
    Route::put('/{exam}', [ExamController::class, 'update']);
    Route::delete('/{exam}', [ExamController::class, 'destroy']);

    // Exam actions
    Route::post('/{exam}/complete', [ExamController::class, 'complete']);
    Route::get('/{exam}/statistics', [ExamController::class, 'statistics']);

    // Exam Students
    Route::get('/{exam}/students', [ExamStudentController::class, 'index']);
    Route::post('/{exam}/students', [ExamStudentController::class, 'store']);
    Route::get('/{exam}/students/{student}', [ExamStudentController::class, 'show']);
    Route::delete('/{exam}/students/{student}', [ExamStudentController::class, 'destroy']);
    Route::put('/{exam}/students/{student}/final-mark', [ExamStudentController::class, 'updateFinalMark']);

    // Exam Questions
    Route::get('/{exam}/questions', [ExamQuestionController::class, 'index']);
    Route::post('/{exam}/questions', [ExamQuestionController::class, 'store']);
    Route::post('/{exam}/questions/bulk-assign', [ExamQuestionController::class, 'bulkAssign']);
    Route::get('/{exam}/students/{student}/questions', [ExamQuestionController::class, 'getStudentQuestions']);
    Route::delete('/{exam}/students/{student}/questions/{question}', [ExamQuestionController::class, 'destroy']);

    // Exam Results
    Route::get('/{exam}/results', [ExamResultController::class, 'index']);
    Route::get('/{exam}/results/summary', [ExamResultController::class, 'summary']);
    Route::post('/{exam}/results/bulk', [ExamResultController::class, 'bulkStore']);
    Route::post('/{exam}/students/{student}/questions/{question}/result', [ExamResultController::class, 'store']);
    Route::get('/{exam}/students/{student}/questions/{question}/result', [ExamResultController::class, 'show']);
    Route::delete('/{exam}/students/{student}/questions/{question}/result', [ExamResultController::class, 'destroy']);
});
