<?php

declare(strict_types=1);

use App\Http\Controllers\Teacher\AttendanceController;
use App\Http\Controllers\Teacher\ClassroomController;
use App\Http\Controllers\Teacher\TodoController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth:teacher', 'verified'])->group(function () {

    Route::get('/', function () {
        return Inertia::render('welcome');
    })->name('home');

    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::get('classrooms', [ClassroomController::class, 'index'])->name(
        'classrooms',
    );

    Route::get('classroom/{classroom}/home', [
        ClassroomController::class,
        'show',
    ])->name('classroom');

    Route::get('todos/{classroom}', [TodoController::class, 'index'])->name(
        'todos',
    );

    Route::post('todos', [TodoController::class, 'store'])->name('todo.store');

    Route::post('todos/{todo}/done', [TodoController::class, 'done'])->name(
        'todo.done',
    );

    Route::post('todos/{todo}/undone', [
        TodoController::class,
        'rollback',
    ])->name('todo.undone');

    Route::delete('classroom/todo/{todo}', [
        TodoController::class,
        'destroy',
    ])->name('todo.destroy');

    Route::post('todos/{id}/restore', [
        TodoController::class,
        'restore',
    ])->name('todo.restore');

    Route::get('classroom/{classroom}/attendance', [
        AttendanceController::class,
        'index',
    ])->name('attendance');

    Route::post('classroom/{classroom}/attendance', [
        AttendanceController::class,
        'store',
    ])->name('attendance.store');

    Route::put('classroom/{classroom}/attendance', [
        AttendanceController::class,
        'update',
    ])->name('attendance.update');

    Route::get('test', function () {
        return Inertia::render('test');
    })->name('test');
});

Route::get('/quran', function () {
    return view('Quran');
});

require __DIR__.'/auth.php';
