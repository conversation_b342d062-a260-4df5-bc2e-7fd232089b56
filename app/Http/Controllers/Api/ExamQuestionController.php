<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Models\Question;
use App\Models\Student;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ExamQuestionController extends Controller
{
    /**
     * Get questions assigned to students in an exam
     */
    public function index(Request $request, Exam $exam): JsonResponse
    {
        $query = $exam->examQuestions()
            ->with(['student', 'question.verse', 'examResult']);

        // Filter by student if provided
        if ($request->has('student_id')) {
            $query->where('student_id', $request->student_id);
        }

        $examQuestions = $query->get();

        return response()->json($examQuestions);
    }

    /**
     * Assign questions to a student in an exam
     */
    public function store(Request $request, Exam $exam): JsonResponse
    {
        $validated = $request->validate([
            'student_id' => 'required|uuid|exists:students,id',
            'question_ids' => 'required|array',
            'question_ids.*' => 'uuid|exists:questions,id',
        ]);

        // Prevent modifying completed exams
        if ($exam->isCompleted()) {
            return response()->json(['error' => 'Cannot modify completed exam'], 422);
        }

        // Verify student is assigned to exam
        $examStudent = $exam->examStudents()
            ->where('student_id', $validated['student_id'])
            ->first();

        if (! $examStudent) {
            return response()->json(['error' => 'Student not assigned to this exam'], 422);
        }

        $assignedQuestions = [];
        foreach ($validated['question_ids'] as $questionId) {
            $examQuestion = ExamQuestion::firstOrCreate([
                'exam_id' => $exam->id,
                'student_id' => $validated['student_id'],
                'question_id' => $questionId,
            ]);

            $examQuestion->load(['student', 'question.verse']);
            $assignedQuestions[] = $examQuestion;
        }

        return response()->json($assignedQuestions, 201);
    }

    /**
     * Remove a question from a student in an exam
     */
    public function destroy(Exam $exam, Student $student, Question $question): JsonResponse
    {
        // Prevent modifying completed exams
        if ($exam->isCompleted()) {
            return response()->json(['error' => 'Cannot modify completed exam'], 422);
        }

        $examQuestion = ExamQuestion::where('exam_id', $exam->id)
            ->where('student_id', $student->id)
            ->where('question_id', $question->id)
            ->first();

        if (! $examQuestion) {
            return response()->json(['error' => 'Question not assigned to this student in this exam'], 404);
        }

        $examQuestion->delete();

        return response()->json(['message' => 'Question removed from student successfully']);
    }

    /**
     * Get questions for a specific student in an exam
     */
    public function getStudentQuestions(Exam $exam, Student $student): JsonResponse
    {
        $examQuestions = ExamQuestion::where('exam_id', $exam->id)
            ->where('student_id', $student->id)
            ->with(['question.verse', 'examResult'])
            ->get();

        return response()->json($examQuestions);
    }

    /**
     * Bulk assign questions to multiple students
     */
    public function bulkAssign(Request $request, Exam $exam): JsonResponse
    {
        $validated = $request->validate([
            'assignments' => 'required|array',
            'assignments.*.student_id' => 'required|uuid|exists:students,id',
            'assignments.*.question_ids' => 'required|array',
            'assignments.*.question_ids.*' => 'uuid|exists:questions,id',
        ]);

        // Prevent modifying completed exams
        if ($exam->isCompleted()) {
            return response()->json(['error' => 'Cannot modify completed exam'], 422);
        }

        $results = [];
        foreach ($validated['assignments'] as $assignment) {
            $studentId = $assignment['student_id'];

            // Verify student is assigned to exam
            $examStudent = $exam->examStudents()
                ->where('student_id', $studentId)
                ->first();

            if (! $examStudent) {
                $results[] = [
                    'student_id' => $studentId,
                    'error' => 'Student not assigned to this exam',
                ];

                continue;
            }

            $assignedQuestions = [];
            foreach ($assignment['question_ids'] as $questionId) {
                $examQuestion = ExamQuestion::firstOrCreate([
                    'exam_id' => $exam->id,
                    'student_id' => $studentId,
                    'question_id' => $questionId,
                ]);

                $assignedQuestions[] = $examQuestion->id;
            }

            $results[] = [
                'student_id' => $studentId,
                'assigned_questions' => $assignedQuestions,
            ];
        }

        return response()->json($results, 201);
    }
}
