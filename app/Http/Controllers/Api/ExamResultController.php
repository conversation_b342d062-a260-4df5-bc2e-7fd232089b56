<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamQuestion;
use App\Models\ExamResult;
use App\Models\Question;
use App\Models\Student;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ExamResultController extends Controller
{
    /**
     * Get results for an exam
     */
    public function index(Exam $exam): JsonResponse
    {
        $results = ExamResult::whereHas('examQuestion', function ($query) use ($exam) {
            $query->where('exam_id', $exam->id);
        })->with([
            'examQuestion.student',
            'examQuestion.question.verse',
        ])->get();

        return response()->json($results);
    }

    /**
     * Store or update a rating for a question
     */
    public function store(Request $request, Exam $exam, Student $student, Question $question): JsonResponse
    {
        $validated = $request->validate([
            'memorization' => 'nullable|integer|min:0|max:255',
            'tajweed_rules' => 'nullable|integer|min:0|max:255',
            'narration_principles' => 'nullable|integer|min:0|max:255',
            'stopping_starting_rules' => 'nullable|integer|min:0|max:255',
            'recitation_performance' => 'nullable|integer|min:0|max:255',
            'sound' => 'nullable|integer|min:0|max:255',
        ]);

        // Find the exam question
        $examQuestion = ExamQuestion::where('exam_id', $exam->id)
            ->where('student_id', $student->id)
            ->where('question_id', $question->id)
            ->first();

        if (! $examQuestion) {
            return response()->json(['error' => 'Question not assigned to this student in this exam'], 404);
        }

        // Create or update the result
        $examResult = ExamResult::updateOrCreate(
            ['exam_question_id' => $examQuestion->id],
            $validated
        );

        // The total score will be calculated automatically via the model's boot method
        $examResult->load(['examQuestion.student', 'examQuestion.question.verse']);

        return response()->json($examResult);
    }

    /**
     * Get a specific result
     */
    public function show(Exam $exam, Student $student, Question $question): JsonResponse
    {
        $examQuestion = ExamQuestion::where('exam_id', $exam->id)
            ->where('student_id', $student->id)
            ->where('question_id', $question->id)
            ->with(['examResult'])
            ->first();

        if (! $examQuestion) {
            return response()->json(['error' => 'Question not assigned to this student in this exam'], 404);
        }

        if (! $examQuestion->examResult) {
            return response()->json(['error' => 'No result found for this question'], 404);
        }

        $examQuestion->examResult->load(['examQuestion.student', 'examQuestion.question.verse']);

        return response()->json($examQuestion->examResult);
    }

    /**
     * Delete a result
     */
    public function destroy(Exam $exam, Student $student, Question $question): JsonResponse
    {
        $examQuestion = ExamQuestion::where('exam_id', $exam->id)
            ->where('student_id', $student->id)
            ->where('question_id', $question->id)
            ->first();

        if (! $examQuestion) {
            return response()->json(['error' => 'Question not assigned to this student in this exam'], 404);
        }

        if (! $examQuestion->examResult) {
            return response()->json(['error' => 'No result found for this question'], 404);
        }

        $examQuestion->examResult->delete();

        return response()->json(['message' => 'Result deleted successfully']);
    }

    /**
     * Bulk update results for multiple questions
     */
    public function bulkStore(Request $request, Exam $exam): JsonResponse
    {
        $validated = $request->validate([
            'results' => 'required|array',
            'results.*.student_id' => 'required|uuid|exists:students,id',
            'results.*.question_id' => 'required|uuid|exists:questions,id',
            'results.*.memorization' => 'nullable|integer|min:0|max:255',
            'results.*.tajweed_rules' => 'nullable|integer|min:0|max:255',
            'results.*.narration_principles' => 'nullable|integer|min:0|max:255',
            'results.*.stopping_starting_rules' => 'nullable|integer|min:0|max:255',
            'results.*.recitation_performance' => 'nullable|integer|min:0|max:255',
            'results.*.sound' => 'nullable|integer|min:0|max:255',
        ]);

        $results = [];
        foreach ($validated['results'] as $resultData) {
            $examQuestion = ExamQuestion::where('exam_id', $exam->id)
                ->where('student_id', $resultData['student_id'])
                ->where('question_id', $resultData['question_id'])
                ->first();

            if (! $examQuestion) {
                $results[] = [
                    'student_id' => $resultData['student_id'],
                    'question_id' => $resultData['question_id'],
                    'error' => 'Question not assigned to this student in this exam',
                ];

                continue;
            }

            $ratingData = array_filter($resultData, function ($key) {
                return in_array($key, [
                    'memorization', 'tajweed_rules', 'narration_principles',
                    'stopping_starting_rules', 'recitation_performance', 'sound',
                ]);
            }, ARRAY_FILTER_USE_KEY);

            $examResult = ExamResult::updateOrCreate(
                ['exam_question_id' => $examQuestion->id],
                $ratingData
            );

            $results[] = [
                'student_id' => $resultData['student_id'],
                'question_id' => $resultData['question_id'],
                'result_id' => $examResult->id,
                'total_score' => $examResult->total_score,
            ];
        }

        return response()->json($results);
    }

    /**
     * Get results summary for an exam
     */
    public function summary(Exam $exam): JsonResponse
    {
        $summary = [
            'total_questions' => $exam->examQuestions()->count(),
            'rated_questions' => $exam->examQuestions()->whereHas('examResult')->count(),
            'students_summary' => [],
        ];

        foreach ($exam->examStudents as $examStudent) {
            $studentQuestions = $examStudent->examQuestions()->count();
            $ratedQuestions = $examStudent->examQuestions()->whereHas('examResult')->count();

            $summary['students_summary'][] = [
                'student_id' => $examStudent->student_id,
                'student_name' => $examStudent->student->name,
                'total_questions' => $studentQuestions,
                'rated_questions' => $ratedQuestions,
                'completion_percentage' => $studentQuestions > 0 ? ($ratedQuestions / $studentQuestions) * 100 : 0,
                'final_mark' => $examStudent->final_mark,
            ];
        }

        return response()->json($summary);
    }
}
