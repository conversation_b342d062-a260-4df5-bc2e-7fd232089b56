<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamStudent;
use App\Models\Student;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ExamStudentController extends Controller
{
    /**
     * Get students assigned to an exam
     */
    public function index(Exam $exam): JsonResponse
    {
        $examStudents = $exam->examStudents()
            ->with(['student', 'examQuestions.question', 'examQuestions.examResult'])
            ->get();

        return response()->json($examStudents);
    }

    /**
     * Assign students to an exam
     */
    public function store(Request $request, Exam $exam): JsonResponse
    {
        $validated = $request->validate([
            'student_ids' => 'required|array',
            'student_ids.*' => 'uuid|exists:students,id',
        ]);

        // Prevent modifying completed exams
        if ($exam->isCompleted()) {
            return response()->json(['error' => 'Cannot modify completed exam'], 422);
        }

        $assignedStudents = [];
        foreach ($validated['student_ids'] as $studentId) {
            $examStudent = ExamStudent::firstOrCreate([
                'exam_id' => $exam->id,
                'student_id' => $studentId,
            ]);

            $examStudent->load(['student']);
            $assignedStudents[] = $examStudent;
        }

        return response()->json($assignedStudents, 201);
    }

    /**
     * Remove a student from an exam
     */
    public function destroy(Exam $exam, Student $student): JsonResponse
    {
        // Prevent modifying completed exams
        if ($exam->isCompleted()) {
            return response()->json(['error' => 'Cannot modify completed exam'], 422);
        }

        $examStudent = ExamStudent::where('exam_id', $exam->id)
            ->where('student_id', $student->id)
            ->first();

        if (! $examStudent) {
            return response()->json(['error' => 'Student not assigned to this exam'], 404);
        }

        $examStudent->delete();

        return response()->json(['message' => 'Student removed from exam successfully']);
    }

    /**
     * Get detailed information for a specific student in an exam
     */
    public function show(Exam $exam, Student $student): JsonResponse
    {
        $examStudent = ExamStudent::where('exam_id', $exam->id)
            ->where('student_id', $student->id)
            ->with([
                'student',
                'examQuestions.question.verse',
                'examQuestions.examResult',
            ])
            ->first();

        if (! $examStudent) {
            return response()->json(['error' => 'Student not assigned to this exam'], 404);
        }

        return response()->json($examStudent);
    }

    /**
     * Update final mark for a student (manual override)
     */
    public function updateFinalMark(Request $request, Exam $exam, Student $student): JsonResponse
    {
        $validated = $request->validate([
            'final_mark' => 'required|numeric|min:0|max:100',
        ]);

        $examStudent = ExamStudent::where('exam_id', $exam->id)
            ->where('student_id', $student->id)
            ->first();

        if (! $examStudent) {
            return response()->json(['error' => 'Student not assigned to this exam'], 404);
        }

        $examStudent->update(['final_mark' => $validated['final_mark']]);
        $examStudent->load(['student']);

        return response()->json($examStudent);
    }
}
