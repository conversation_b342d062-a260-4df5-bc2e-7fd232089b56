<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\Teacher;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ExamController extends Controller
{
    /**
     * Display a listing of exams
     */
    public function index(Request $request): JsonResponse
    {
        $query = Exam::with(['teacher', 'students']);

        // Filter by teacher if provided
        if ($request->has('teacher_id')) {
            $query->where('teacher_id', $request->teacher_id);
        }

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $exams = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json($exams);
    }

    /**
     * Store a newly created exam
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'teacher_id' => 'required|uuid|exists:teachers,id',
            'status' => ['sometimes', Rule::in(['draft', 'active'])],
        ]);

        $exam = Exam::create($validated);
        $exam->load(['teacher', 'students']);

        return response()->json($exam, 201);
    }

    /**
     * Display the specified exam
     */
    public function show(Exam $exam): JsonResponse
    {
        $exam->load([
            'teacher',
            'students',
            'examQuestions.question',
            'examQuestions.examResult',
        ]);

        return response()->json($exam);
    }

    /**
     * Update the specified exam
     */
    public function update(Request $request, Exam $exam): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'status' => ['sometimes', Rule::in(['draft', 'active', 'completed'])],
        ]);

        // Prevent updating completed exams
        if ($exam->isCompleted() && isset($validated['status']) && $validated['status'] !== 'completed') {
            return response()->json(['error' => 'Cannot modify completed exam'], 422);
        }

        $exam->update($validated);
        $exam->load(['teacher', 'students']);

        return response()->json($exam);
    }

    /**
     * Remove the specified exam
     */
    public function destroy(Exam $exam): JsonResponse
    {
        // Prevent deleting completed exams
        if ($exam->isCompleted()) {
            return response()->json(['error' => 'Cannot delete completed exam'], 422);
        }

        $exam->delete();

        return response()->json(['message' => 'Exam deleted successfully']);
    }

    /**
     * Complete an exam
     */
    public function complete(Exam $exam): JsonResponse
    {
        if ($exam->isCompleted()) {
            return response()->json(['error' => 'Exam is already completed'], 422);
        }

        if (! $exam->isFullyRated()) {
            return response()->json(['error' => 'All students must be rated before completing exam'], 422);
        }

        // Calculate final marks for all students
        foreach ($exam->examStudents as $examStudent) {
            $examStudent->calculateFinalMark();
        }

        $exam->markAsCompleted();
        $exam->load(['teacher', 'students']);

        return response()->json($exam);
    }

    /**
     * Get exam statistics
     */
    public function statistics(Exam $exam): JsonResponse
    {
        $stats = [
            'total_students' => $exam->students()->count(),
            'total_questions' => $exam->examQuestions()->count(),
            'rated_questions' => $exam->examQuestions()->whereHas('examResult')->count(),
            'completion_percentage' => 0,
            'average_score' => 0,
        ];

        if ($stats['total_questions'] > 0) {
            $stats['completion_percentage'] = ($stats['rated_questions'] / $stats['total_questions']) * 100;
        }

        if ($exam->isCompleted()) {
            $stats['average_score'] = $exam->examStudents()
                ->whereNotNull('final_mark')
                ->avg('final_mark') ?? 0;
        }

        return response()->json($stats);
    }
}
