<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Exam extends Model
{
    use HasUuids;

    protected $fillable = [
        'name',
        'status',
        'teacher_id',
        'completed_at',
    ];

    protected $casts = [
        'completed_at' => 'datetime',
    ];

    /**
     * Get the teacher that owns the exam
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    /**
     * Get all students assigned to this exam
     */
    public function students(): BelongsToMany
    {
        return $this->belongsToMany(Student::class, 'exam_students')
            ->withPivot('final_mark')
            ->withTimestamps();
    }

    /**
     * Get all exam student records
     */
    public function examStudents(): HasMany
    {
        return $this->hasMany(ExamStudent::class);
    }

    /**
     * Get all exam questions
     */
    public function examQuestions(): Has<PERSON><PERSON>
    {
        return $this->hasMany(ExamQuestion::class);
    }

    /**
     * Check if exam is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if exam is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if exam is draft
     */
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Mark exam as completed
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Get questions for a specific student in this exam
     */
    public function getQuestionsForStudent(Student $student)
    {
        return $this->examQuestions()
            ->where('student_id', $student->id)
            ->with(['question', 'examResult'])
            ->get();
    }

    /**
     * Check if all students have been rated on all their questions
     */
    public function isFullyRated(): bool
    {
        $totalQuestions = $this->examQuestions()->count();
        $ratedQuestions = $this->examQuestions()
            ->whereHas('examResult')
            ->count();

        return $totalQuestions > 0 && $totalQuestions === $ratedQuestions;
    }
}
