<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ExamStudent extends Model
{
    use HasUuids;

    protected $fillable = [
        'exam_id',
        'student_id',
        'final_mark',
    ];

    protected $casts = [
        'final_mark' => 'decimal:2',
    ];

    /**
     * Get the exam that owns this record
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * Get the student that owns this record
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get all exam questions for this student in this exam
     */
    public function examQuestions(): HasMany
    {
        return $this->hasMany(ExamQuestion::class, 'student_id', 'student_id')
            ->where('exam_id', $this->exam_id);
    }

    /**
     * Calculate and update the final mark for this student
     */
    public function calculateFinalMark(): void
    {
        $examQuestions = $this->examQuestions()->with('examResult')->get();

        if ($examQuestions->isEmpty()) {
            return;
        }

        $totalScore = 0;
        $questionCount = 0;

        foreach ($examQuestions as $examQuestion) {
            if ($examQuestion->examResult) {
                $totalScore += $examQuestion->examResult->total_score ?? 0;
                $questionCount++;
            }
        }

        $finalMark = $questionCount > 0 ? $totalScore / $questionCount : 0;

        $this->update(['final_mark' => $finalMark]);
    }

    /**
     * Check if all questions for this student have been rated
     */
    public function isFullyRated(): bool
    {
        $totalQuestions = $this->examQuestions()->count();
        $ratedQuestions = $this->examQuestions()
            ->whereHas('examResult')
            ->count();

        return $totalQuestions > 0 && $totalQuestions === $ratedQuestions;
    }
}
