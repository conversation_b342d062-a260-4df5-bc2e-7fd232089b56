<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamResult extends Model
{
    use HasUuids;

    protected $fillable = [
        'exam_question_id',
        'memorization',
        'tajweed_rules',
        'narration_principles',
        'stopping_starting_rules',
        'recitation_performance',
        'sound',
        'total_score',
    ];

    protected $casts = [
        'total_score' => 'decimal:2',
    ];

    /**
     * Get the exam question that owns this result
     */
    public function examQuestion(): BelongsTo
    {
        return $this->belongsTo(ExamQuestion::class);
    }

    /**
     * Calculate total score based on settings and ratings
     */
    public function calculateTotalScore(): void
    {
        $settings = $this->getSettingsWeights();
        $totalScore = 0;

        foreach ($settings as $category => $config) {
            $rating = $this->{$category} ?? 0;
            $weight = $config['weight'];
            $deduction = $config['deduction_per_mistake'];

            // Calculate score for this category
            // Assuming rating is number of mistakes, so score = weight - (mistakes * deduction)
            $categoryScore = max(0, $weight - ($rating * $deduction));
            $totalScore += $categoryScore;
        }

        $this->update(['total_score' => $totalScore]);
    }

    /**
     * Boot method to automatically calculate total score when ratings are updated
     */
    protected static function boot(): void
    {
        parent::boot();

        static::saved(function ($examResult) {
            $examResult->calculateTotalScore();
        });
    }

    /**
     * Get settings weights from the database
     */
    private function getSettingsWeights(): array
    {
        $defaultSettings = [
            'memorization' => ['weight' => 60, 'deduction_per_mistake' => 2],
            'tajweed_rules' => ['weight' => 12.5, 'deduction_per_mistake' => 0.5],
            'narration_principles' => ['weight' => 10, 'deduction_per_mistake' => 1],
            'stopping_starting_rules' => ['weight' => 5, 'deduction_per_mistake' => 0.5],
            'recitation_performance' => ['weight' => 7.5, 'deduction_per_mistake' => 1],
            'sound' => ['weight' => 5, 'deduction_per_mistake' => 0.25],
        ];

        $settings = [];
        foreach ($defaultSettings as $key => $default) {
            $setting = Setting::where('key', $key)->first();
            $settings[$key] = $setting ? $setting->value : $default;
        }

        return $settings;
    }
}
