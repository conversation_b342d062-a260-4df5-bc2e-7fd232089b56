<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Question extends Model
{
    use HasUuids;

    protected $fillable = [
        'teacher_id',
        'verse_id',
        'start_verse_id',
        'end_verse_id',
        'status',
        'type',
    ];

    public function teacher(): Bel<PERSON><PERSON>T<PERSON>
    {
        return $this->belongsTo(Teacher::class);
    }

    public function verse(): BelongsTo
    {
        return $this->belongsTo(Verse::class);
    }

    public function startVerse(): BelongsTo
    {
        return $this->belongsTo(Verse::class, 'start_verse_id');
    }

    public function endVerse(): BelongsTo
    {
        return $this->belongsTo(Verse::class, 'end_verse_id');
    }
}
