<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Verse extends Model
{
    use HasUuids;

    protected $fillable = [
        'number',
        'text',
        'juz',
        'page',
        'line_start',
        'line_end',
    ];

    /**
     * Helper to strip Arabic diacritics from a string
     */
    public static function removeArabicDiacritics(string $text): string
    {
        return preg_replace('/[\x{0610}-\x{061A}\x{064B}-\x{065F}\x{0670}]/u', '', $text);
    }

    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    /**
     * Scope to search verses with diacritic-insensitive text
     */
    public function scopeWhereTextStartsWith(Builder $query, string $text): Builder
    {
        $normalizedInput = self::removeArabicDiacritics($text);

        return $query->whereRaw(
            "REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(text,
                'َ',''),'ً',''),'ُ',''),'ٌ',''),'ِ',''),'ٍ',''),'ْ',''),'ّ',''),'ٓ',''),'ٰ','')
                LIKE ?",
            [$normalizedInput.'%']
        );
    }
}
