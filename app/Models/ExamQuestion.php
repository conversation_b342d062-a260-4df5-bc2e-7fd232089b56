<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ExamQuestion extends Model
{
    use HasUuids;

    protected $fillable = [
        'exam_id',
        'student_id',
        'question_id',
    ];

    /**
     * Get the exam that owns this record
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * Get the student that owns this record
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the question that owns this record
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class);
    }

    /**
     * Get the exam result for this question
     */
    public function examResult(): HasOne
    {
        return $this->hasOne(ExamResult::class);
    }

    /**
     * Check if this question has been rated
     */
    public function isRated(): bool
    {
        return $this->examResult()->exists();
    }

    /**
     * Get the exam student record for this question
     */
    public function examStudent(): BelongsTo
    {
        return $this->belongsTo(ExamStudent::class, 'student_id', 'student_id')
            ->where('exam_id', $this->exam_id);
    }
}
