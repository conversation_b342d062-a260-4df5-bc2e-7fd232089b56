<?php

declare(strict_types=1);

namespace App\Filament\Resources\ExamResource\Pages;

use App\Filament\Resources\ExamResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditExam extends EditRecord
{
    protected static string $resource = ExamResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make()
                ->visible(fn ($record) => ! $record->isCompleted()),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Prevent updating completed exams
        if ($this->record->isCompleted() && isset($data['status']) && $data['status'] !== 'completed') {
            unset($data['status']);
        }

        return $data;
    }
}
