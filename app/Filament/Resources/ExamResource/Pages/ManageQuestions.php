<?php

declare(strict_types=1);

namespace App\Filament\Resources\ExamResource\Pages;

use App\Filament\Resources\ExamResource;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ManageQuestions extends ManageRelatedRecords
{
    protected static string $resource = ExamResource::class;

    protected static string $relationship = 'examQuestions';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-question-mark-circle';

    public static function getNavigationLabel(): string
    {
        return 'Questions';
    }

    public function form(Schema $form): Schema
    {
        return $form
            ->schema([
                Select::make('student_id')
                    ->relationship('student', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->label('Student')
                    ->options(function () {
                        // Only show students assigned to this exam
                        return $this->getOwnerRecord()
                            ->students()
                            ->pluck('name', 'id')
                            ->toArray();
                    }),

                Select::make('question_id')
                    ->relationship('question', 'id')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->label('Question')
                    ->getOptionLabelFromRecordUsing(function ($record) {
                        if ($record->verse) {
                            return "Verse {$record->verse->verse_number} - {$record->verse->chapter->name}";
                        }

                        return "Question {$record->id}";
                    }),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('question.id')
            ->columns([
                TextColumn::make('student.name')
                    ->searchable()
                    ->sortable()
                    ->label('Student'),

                TextColumn::make('question.verse.chapter.name')
                    ->label('Chapter')
                    ->searchable(),

                TextColumn::make('question.verse.verse_number')
                    ->label('Verse Number')
                    ->sortable(),

                TextColumn::make('question.type')
                    ->badge()
                    ->label('Question Type'),

                IconColumn::make('is_rated')
                    ->getStateUsing(fn ($record) => $record->examResult !== null)
                    ->boolean()
                    ->label('Rated'),

                TextColumn::make('examResult.total_score')
                    ->numeric(decimalPlaces: 2)
                    ->label('Score')
                    ->badge()
                    ->color(fn ($state) => $state >= 70 ? 'success' : ($state >= 50 ? 'warning' : 'danger')),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('student_id')
                    ->relationship('student', 'name')
                    ->label('Student'),

                SelectFilter::make('is_rated')
                    ->options([
                        '1' => 'Rated',
                        '0' => 'Not Rated',
                    ])
                    ->query(function ($query, $data) {
                        if ($data['value'] === '1') {
                            return $query->whereHas('examResult');
                        }
                        if ($data['value'] === '0') {
                            return $query->whereDoesntHave('examResult');
                        }

                        return $query;
                    }),
            ])
            ->headerActions([
                CreateAction::make()
                    ->visible(fn () => ! $this->getOwnerRecord()->isCompleted()),
            ])
            ->actions([
                EditAction::make()
                    ->visible(fn () => ! $this->getOwnerRecord()->isCompleted()),
                DeleteAction::make()
                    ->visible(fn () => ! $this->getOwnerRecord()->isCompleted()),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->visible(fn () => ! $this->getOwnerRecord()->isCompleted()),
                ]),
            ]);
    }
}
