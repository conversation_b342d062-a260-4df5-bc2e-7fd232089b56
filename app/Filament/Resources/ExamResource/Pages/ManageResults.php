<?php

declare(strict_types=1);

namespace App\Filament\Resources\ExamResource\Pages;

use App\Filament\Resources\ExamResource;
use App\Models\ExamResult;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ManageResults extends ManageRelatedRecords
{
    protected static string $resource = ExamResource::class;

    protected static string $relationship = 'examQuestions';

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-chart-bar';

    public static function getNavigationLabel(): string
    {
        return 'Results';
    }

    public function form(Schema $form): Schema
    {
        return $form
            ->schema([
                Section::make('Question Details')
                    ->schema([
                        Select::make('exam_question_id')
                            ->label('Question Assignment')
                            ->options(function () {
                                return $this->getOwnerRecord()
                                    ->examQuestions()
                                    ->with(['student', 'question.verse.chapter'])
                                    ->get()
                                    ->mapWithKeys(function ($examQuestion) {
                                        $label = $examQuestion->student->name.' - ';
                                        if ($examQuestion->question->verse) {
                                            $label .= $examQuestion->question->verse->chapter->name.
                                                     ' (Verse '.$examQuestion->question->verse->verse_number.')';
                                        } else {
                                            $label .= 'Question '.$examQuestion->question->id;
                                        }

                                        return [$examQuestion->id => $label];
                                    })
                                    ->toArray();
                            })
                            ->required()
                            ->searchable()
                            ->disabled(fn ($record) => $record !== null),
                    ]),

                Section::make('Rating Criteria')
                    ->description('Enter the number of mistakes for each category')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('memorization')
                                    ->label('Memorization (الحفظ)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(255)
                                    ->default(0)
                                    ->helperText('Number of memorization mistakes'),

                                TextInput::make('tajweed_rules')
                                    ->label('Tajweed Rules (الأحكام)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(255)
                                    ->default(0)
                                    ->helperText('Number of tajweed mistakes'),

                                TextInput::make('narration_principles')
                                    ->label('Narration Principles (أصل الرواية)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(255)
                                    ->default(0)
                                    ->helperText('Number of narration mistakes'),

                                TextInput::make('stopping_starting_rules')
                                    ->label('Stopping/Starting Rules (الوقف والبداية)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(255)
                                    ->default(0)
                                    ->helperText('Number of stopping/starting mistakes'),

                                TextInput::make('recitation_performance')
                                    ->label('Recitation Performance (الأداء)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(255)
                                    ->default(0)
                                    ->helperText('Number of performance mistakes'),

                                TextInput::make('sound')
                                    ->label('Sound (الصوت)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(255)
                                    ->default(0)
                                    ->helperText('Number of sound mistakes'),
                            ]),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('examQuestion.student.name')
            ->modifyQueryUsing(function ($query) {
                return $query->with(['examResult']);
            })
            ->columns([
                TextColumn::make('student.name')
                    ->searchable()
                    ->sortable()
                    ->label('Student'),

                TextColumn::make('question.verse.chapter.name')
                    ->label('Chapter')
                    ->searchable(),

                TextColumn::make('question.verse.verse_number')
                    ->label('Verse')
                    ->sortable(),

                TextColumn::make('examResult.memorization')
                    ->label('Memorization Mistakes')
                    ->default('Not Rated'),

                TextColumn::make('examResult.tajweed_rules')
                    ->label('Tajweed Mistakes')
                    ->default('Not Rated'),

                TextColumn::make('examResult.total_score')
                    ->numeric(decimalPlaces: 2)
                    ->label('Total Score')
                    ->badge()
                    ->color(fn ($state) => $state >= 70 ? 'success' : ($state >= 50 ? 'warning' : 'danger'))
                    ->default('Not Rated'),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('student_id')
                    ->relationship('student', 'name')
                    ->label('Student'),

                SelectFilter::make('is_rated')
                    ->options([
                        '1' => 'Rated',
                        '0' => 'Not Rated',
                    ])
                    ->query(function ($query, $data) {
                        if ($data['value'] === '1') {
                            return $query->whereHas('examResult');
                        }
                        if ($data['value'] === '0') {
                            return $query->whereDoesntHave('examResult');
                        }

                        return $query;
                    }),
            ])
            ->headerActions([
                Action::make('complete_exam')
                    ->label('Complete Exam')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn () => ! $this->getOwnerRecord()->isCompleted())
                    ->requiresConfirmation()
                    ->action(function () {
                        $exam = $this->getOwnerRecord();

                        if (! $exam->isFullyRated()) {
                            Notification::make()
                                ->title('Cannot complete exam')
                                ->body('All students must be rated on all their questions before completing the exam.')
                                ->danger()
                                ->send();

                            return;
                        }

                        // Calculate final marks for all students
                        foreach ($exam->examStudents as $examStudent) {
                            $examStudent->calculateFinalMark();
                        }

                        $exam->markAsCompleted();

                        Notification::make()
                            ->title('Exam completed successfully')
                            ->success()
                            ->send();
                    }),
            ])
            ->actions([
                EditAction::make()
                    ->label('Rate')
                    ->modalHeading('Rate Question')
                    ->form(function () {
                        return $this->form(new Schema());
                    })
                    ->fillForm(function ($record) {
                        if ($record->examResult) {
                            return [
                                'exam_question_id' => $record->id,
                                'memorization' => $record->examResult->memorization,
                                'tajweed_rules' => $record->examResult->tajweed_rules,
                                'narration_principles' => $record->examResult->narration_principles,
                                'stopping_starting_rules' => $record->examResult->stopping_starting_rules,
                                'recitation_performance' => $record->examResult->recitation_performance,
                                'sound' => $record->examResult->sound,
                            ];
                        }

                        return ['exam_question_id' => $record->id];
                    })
                    ->using(function ($record, $data) {
                        $resultData = collect($data)->except('exam_question_id')->toArray();

                        ExamResult::updateOrCreate(
                            ['exam_question_id' => $record->id],
                            $resultData
                        );

                        return $record;
                    }),

                DeleteAction::make()
                    ->label('Delete Rating')
                    ->visible(fn ($record) => $record->examResult !== null)
                    ->action(function ($record) {
                        $record->examResult?->delete();
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Delete Ratings')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->examResult?->delete();
                            }
                        }),
                ]),
            ]);
    }
}
