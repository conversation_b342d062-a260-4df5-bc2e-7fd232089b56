<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\ExamResource\Pages\EditExam;
use App\Filament\Resources\ExamResource\Pages\ListExams;
use App\Filament\Resources\ExamResource\Pages\ManageQuestions;
use App\Filament\Resources\ExamResource\Pages\ManageResults;
use App\Filament\Resources\ExamResource\Pages\ManageStudents;
use App\Models\Exam;
use BackedEnum;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Enums\SubNavigationPosition;
use Filament\Pages\Page;
use Filament\Resources\Resource;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use UnitEnum;

class ExamResource extends Resource
{
    protected static ?string $model = Exam::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-academic-cap';

    protected static string|UnitEnum|null $navigationGroup = 'School Management';

    protected static ?int $navigationSort = 3;

    protected static ?SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Schema $form): Schema
    {
        return $form
            ->schema([
                Section::make('Exam Details')
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Exam Name'),

                        Select::make('teacher_id')
                            ->relationship('teacher', 'name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->label('Teacher'),

                        Select::make('status')
                            ->options([
                                'draft' => 'Draft',
                                'active' => 'Active',
                                'completed' => 'Completed',
                            ])
                            ->required()
                            ->default('draft')
                            ->disabled(fn ($record) => $record?->isCompleted()),

                        DateTimePicker::make('completed_at')
                            ->label('Completed At')
                            ->disabled()
                            ->visible(fn ($record) => $record?->isCompleted()),
                    ])
                    ->columns(1),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label('Exam Name'),

                TextColumn::make('teacher.name')
                    ->searchable()
                    ->sortable()
                    ->label('Teacher'),

                BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'draft',
                        'warning' => 'active',
                        'success' => 'completed',
                    ])
                    ->icons([
                        'heroicon-o-pencil' => 'draft',
                        'heroicon-o-play' => 'active',
                        'heroicon-o-check-circle' => 'completed',
                    ]),

                TextColumn::make('students_count')
                    ->counts('students')
                    ->label('Students'),

                TextColumn::make('exam_questions_count')
                    ->counts('examQuestions')
                    ->label('Questions'),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('completed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                if (! $record->isCompleted()) {
                                    $record->delete();
                                }
                            }
                        }),
                ]),
            ]);
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditExam::class,
            ManageStudents::class,
            ManageQuestions::class,
            ManageResults::class,
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListExams::route('/'),
            'edit' => EditExam::route('/{record}/edit'),
            'students' => ManageStudents::route('/{record}/students'),
            'questions' => ManageQuestions::route('/{record}/questions'),
            'results' => ManageResults::route('/{record}/results'),
        ];
    }
}
