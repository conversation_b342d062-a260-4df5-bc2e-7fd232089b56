import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { BookmarkSquare } from '@solar-icons/react';
import TodoCards from '@/components/todo/todo-cards';
import Snackbar from '@/components/ui/snack-bar';
import { useTodos, Todo } from '@/hooks/useTodos';
import { useTodoActions } from '@/hooks/useTodoActions';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import BottomSheet from '../ui/bottom-sheet';
import TodoForm from './todo-form';

interface TodoListProps {
    classroom_id: string;
}

export default function TodoList({ classroom_id }: TodoListProps) {
    const {
        groupedTodos,
        page,
        hasMore,
        loading,
        fetchTodos,
        addTodo,
        updateTodo,
        removeTodo,
        reset,
    } = useTodos(classroom_id);

    const { restoreTodo } = useTodoActions();

    const [bottomSheetOpen, setBottomSheetOpen] = useState(false);
    const [showDeleteSnackbar, setShowDeleteSnackbar] = useState(false);
    const [deletedTodo, setDeletedTodo] = useState<Todo | null>(null);

    const [filter, setFilter] = useState<'all' | 'today'>('all');

    const sentinelRef = useInfiniteScroll({
        hasMore,
        loading,
        onLoadMore: () => fetchTodos(page + 1, filter),
    });

    const handleTodoDeleted = (id: string) => {
        const allTodos = Object.values(groupedTodos).flat();
        const todoToDelete = allTodos.find((t) => t.id === id);

        if (todoToDelete) {
            setDeletedTodo(todoToDelete);
            setShowDeleteSnackbar(true);
        }

        removeTodo(id);
    };

    const handleTodoRestore = async () => {
        if (!deletedTodo) return;

        const restoredTodo = await restoreTodo(deletedTodo.id);
        if (restoredTodo) {
            addTodo(restoredTodo);
        }

        setShowDeleteSnackbar(false);
        setDeletedTodo(null);
    };

    // Initialize todos on mount
    useEffect(() => {
        fetchTodos(1, filter);
    }, [filter]);

    if (loading && Object.keys(groupedTodos).length === 0) {
        return <div className="text-center p-4">...جار التحميل</div>;
    }

    return (
        <>
            <div className="bg-secondary-500 rounded-xl p-1 shadow items-center font-semibold text-lg">
                <div className="flex items-center justify-between bg-secondary-300 rounded-xl px-3 py-2 border-b border-secondary-500 shadow-sm">
                    <span className="text-secondary-700">قائمة المهام</span>
                    <div className="flex items-center gap-2">
                        <BookmarkSquare
                            size={24}
                            className="text-secondary-700 mix-blend-luminosity"
                        />
                    </div>
                </div>

                <button
                    onClick={() => setBottomSheetOpen(true)}
                    className="w-full py-1 text-secondary-800 bg-transparent font-medium text-center cursor-pointer"
                >
                    إضافة مهمة جديدة
                </button>
            </div>

            <div className="flex justify-center my-4">
                <div className="w-full bg-secondary-500 flex rounded-lg p-1 relative">
                    <button
                        onClick={() => setFilter('today')}
                        className="px-4 py-2 flex-1 text-sm font-medium rounded-md text-secondary-700 z-10"
                    >
                        مهام اليوم
                    </button>
                    <button
                        onClick={() => setFilter('all')}
                        className="px-4 flex-1 py-2 text-sm font-medium rounded-md text-secondary-700 z-10"
                    >
                        الكل
                    </button>
                    {filter === 'today' && (
                        <motion.div
                            layoutId="active-tab"
                            className="absolute inset-0 bg-secondary-300 rounded-md shadow"
                            style={{
                                width: '50%',
                                right: '0%',
                            }}
                        />
                    )}
                    {filter === 'all' && (
                        <motion.div
                            layoutId="active-tab"
                            className="absolute inset-0 bg-secondary-300 rounded-md shadow"
                            style={{
                                width: '50%',
                                right: '50%',
                            }}
                        />
                    )}
                </div>
            </div>

            {Object.keys(groupedTodos).length > 0 ? (
                <>
                    <TodoCards
                        groupedTodos={groupedTodos}
                        onTodoUpdated={updateTodo}
                        onTodoDeleted={handleTodoDeleted}
                    />
                    <div ref={sentinelRef} className="h-4" />
                </>
            ) : (
                <div className="text-center p-4 text-secondary-700">
                    لا توجد مهام حاليًا.
                </div>
            )}

            <BottomSheet
                open={bottomSheetOpen}
                setOpen={setBottomSheetOpen}
                className="bg-secondary-100"
            >
                <TodoForm
                    classroomId={classroom_id}
                    onTodoCreated={addTodo}
                    onClose={() => setBottomSheetOpen(false)}
                />
            </BottomSheet>

            <Snackbar
                show={showDeleteSnackbar}
                message="تم حذف المهمة"
                actionLabel="استرجاع"
                onAction={handleTodoRestore}
                onClose={() => {
                    setShowDeleteSnackbar(false);
                    setDeletedTodo(null);
                }}
            />
        </>
    );
}
