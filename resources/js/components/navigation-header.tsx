import { ArrowLeft } from '@solar-icons/react';
import { Link, router } from '@inertiajs/react';
import Container from './container';

export default function NavigationHeader({
    title,
    backLink,
}: {
    title: string;
    backLink: string;
}) {
    // const href = route(backLink || 'dashboard');
    return (
        <div className="shadow-sm">
            <Container>
                <div className="flex items-center justify-between h-[75px]">
                    <h1 className="text-xl text-primary-500 font-bold">
                        {title}
                    </h1>

                    <Link
                        href={backLink}
                        as="a"
                        className="flex items-center justify-center bg-primary-50 h-9 w-9 rounded-[12px]"
                    >
                        <ArrowLeft
                            size={24}
                            weight="Linear"
                            className="text-primary-500"
                        />
                    </Link>
                </div>
            </Container>
        </div>
    );
}
