import { useState } from 'react';
import { Drawer } from 'vaul';

const snapPoints = ['400px'];

interface BottomSheetProps {
    open: boolean;
    setOpen: (open: boolean) => void;
    children: React.ReactNode;
    className?: string;
}

export default function BottomSheet({
    open,
    setOpen,
    children,
    className,
}: BottomSheetProps) {
    const [snap, setSnap] = useState<number | string | null>(snapPoints[0]);

    return (
        <Drawer.Root
            open={open}
            onOpenChange={setOpen}
            snapPoints={snapPoints}
            activeSnapPoint={snap}
            setActiveSnapPoint={setSnap}
            snapToSequentialPoint
        >
            <Drawer.Portal>
                <Drawer.Overlay className="fixed inset-0 bg-black/30 backdrop-blur-xs" />
                <Drawer.Content
                    data-testid="content"
                    className={`fixed flex flex-col bg-[#F3E9D9] border border-secondary-700/50 border-b-none rounded-t-[30px] bottom-0 left-0 right-0 h-full max-h-[97%] mx-[-1px] ${className}`}
                >
                    <div className="flex flex-col max-w-md mx-auto w-full p-8 pt-5 overflow-y-auto">
                        {/* indicator at the top */}
                         <div
                            aria-hidden
                            className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-secondary-600 mb-8"
                        />

                        <div className="hidden">
                            <Drawer.Title className="text-2xl mt-2 font-medium text-gray-900"></Drawer.Title>
                            <Drawer.Description className="text-sm text-gray-500">
                                {/* Description goes here */}
                            </Drawer.Description>
                        </div>

                        {children}
                    </div>
                </Drawer.Content>
            </Drawer.Portal>
        </Drawer.Root>
    );
}
