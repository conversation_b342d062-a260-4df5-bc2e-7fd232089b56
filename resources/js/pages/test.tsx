import BottomSheet from '@/components/ui/bottom-sheet';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { useRef, useState } from 'react';

export default function Test() {
    const [sheetOpen, setSheetOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);

    const handleOpenSheet = () => {
        buttonRef.current?.blur();
        setSheetOpen(true);
    };

    return (
        <AppLayout>
            <div className="w-full h-full flex items-center justify-center flex-1">
                <Button
                    variant="outline"
                    onClick={handleOpenSheet}
                    ref={buttonRef}
                >
                    Open sheet
                </Button>
                <BottomSheet
                    open={sheetOpen}
                    setOpen={setSheetOpen}
                    // title="Test Bottom Sheet"
                >
                    <div className="flex flex-col gap-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-[21px] font-bold">
                                    عبدالقادر المبروك
                                </h1>
                                <p className="text-lg text-primary-500 font-bold">
                                    34622634
                                </p>
                            </div>

                            <div>
                                <h1 className="text-left text-[21px] font-bold text-[#A60D10]">
                                    غياب
                                </h1>
                                <p className="text-lg text-primary-500">
                                    2025-08-21
                                </p>
                            </div>
                        </div>

                        <textarea
                            placeholder="ملاحظات"
                            className="bg-secondary-200 p-4 border border-secondary-500 rounded-[12px] h-[150px] focus-visible:outline-none"
                        />

                        <Button className="p-3 h-12 bg-primary-500 text-white text-[16px] rounded-xl">
                            تسجيل الغياب
                        </Button>
                    </div>
                </BottomSheet>
            </div>
        </AppLayout>
    );
}
