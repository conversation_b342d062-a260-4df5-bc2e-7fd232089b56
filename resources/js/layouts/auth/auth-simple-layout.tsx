import Logo from '@/assets/Logo.svg';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({
    children,
    title,
    description,
}: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="flex min-h-svh flex-col items-center bg-background-100 py-[8rem] md:p-10">
            <div className="w-full max-w-sm flex flex-col justify-between flex-1">
                <div className="flex flex-col items-center gap-10 sm:gap-12 md:gap-16">
                    <div className="flex flex-col items-center gap-4">
                        <Logo width={86} height={86} />
                        <span className="sr-only">{title}</span>
                    </div>

                    <div className="text-center">
                        <h1 className="text-[28px] md:text-[32px] text-primary-500 font-baloo-regular">
                            {title}
                        </h1>
                        <p className="text-[20px] md:text-[26px] text-secondary-500 font-bold">
                            {description}
                        </p>
                    </div>
                </div>

                <div className="mt-auto pt-10">{children}</div>
            </div>
        </div>
    );
}
