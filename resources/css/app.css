@import url('https://fonts.googleapis.com/css2?family=Baloo+<PERSON><PERSON><PERSON><PERSON>+2:wght@400..800&display=swap');
@import './font.css';
@import 'tailwindcss';
@import 'tw-animate-css';
@custom-variant dark (&:is(.dark *));

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../../resources/livewire/**/*.blade.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans:
        'Baloo Bhaijaan 2', ui-sans-serif, system-ui, sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
        'Noto Color Emoji';

    --font-baloo-regular: 'Baloo Bhaijaan', 'sans-serif';
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-primary-50: var(--primary-50);
    --color-primary-100: var(--primary-100);
    --color-primary-200: var(--primary-200);
    --color-primary-300: var(--primary-300);
    --color-primary-400: var(--primary-400);
    --color-primary-500: var(--primary-500);
    --color-primary-600: var(--primary-600);
    --color-primary-700: var(--primary-700);
    --color-primary-800: var(--primary-800);
    --color-primary-900: var(--primary-900);

    --color-secondary-50: var(--secondary-50);
    --color-secondary-100: var(--secondary-100);
    --color-secondary-200: var(--secondary-200);
    --color-secondary-300: var(--secondary-300);
    --color-secondary-400: var(--secondary-400);
    --color-secondary-500: var(--secondary-500);
    --color-secondary-600: var(--secondary-600);
    --color-secondary-700: var(--secondary-700);
    --color-secondary-800: var(--secondary-800);
    --color-secondary-900: var(--secondary-900);

    --color-background-50: var(--background-50);
    --color-background-100: var(--background-100);
    --color-background-200: var(--background-200);
    --color-background-300: var(--background-300);
    --color-background-400: var(--background-400);
    --color-background-500: var(--background-500);
    --color-background-600: var(--background-600);
    --color-background-700: var(--background-700);
    --color-background-800: var(--background-800);
    --color-background-900: var(--background-900);

}

:root {
    --radius: 0.625rem;
    --background: rgba(252, 248, 243, 1);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);

    --primary-50: rgba(234, 238, 236, 1);
    --primary-100: rgba(191, 202, 197, 1);
    --primary-200: rgba(160, 176, 169, 1);
    --primary-300: rgba(117, 140, 129, 1);
    --primary-400: rgba(90, 118, 105, 1);
    --primary-500: rgba(49, 84, 67, 1);
    --primary-600: rgba(45, 76, 61, 1);
    --primary-700: rgba(35, 60, 48, 1);
    --primary-800: rgba(27, 46, 37, 1);
    --primary-900: rgba(21, 35, 28, 1);
    --secondary-50: rgba(251, 248, 243, 1);
    --secondary-100: rgba(243, 233, 217, 1);
    --secondary-200: rgba(237, 223, 199, 1);
    --secondary-300: rgba(228, 208, 173, 1);
    --secondary-400: rgba(223, 199, 157, 1);
    --secondary-500: rgba(215, 185, 133, 1);
    --secondary-600: rgba(196, 168, 121, 1);
    --secondary-700: rgba(153, 131, 94, 1);
    --secondary-800: rgba(118, 102, 73, 1);
    --secondary-900: rgba(90, 78, 56, 1);
    --background-50: rgba(254, 253, 251, 1);
    --background-100: rgba(252, 249, 243, 1);
    --background-200: rgba(251, 246, 237, 1);
    --background-300: rgba(249, 242, 229, 1);
    --background-400: rgba(248, 239, 224, 1);
    --background-500: rgba(246, 235, 216, 1);
    --background-600: rgba(224, 214, 197, 1);
    --background-700: rgba(175, 167, 153, 1);
    --background-800: rgba(135, 129, 119, 1);
    --background-900: rgba(103, 99, 91, 1);
}

/* .dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
} */

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
}
