@import '../../../../vendor/filament/filament/resources/css/theme.css';

@source '../../../../app/Filament/**/*';
@source '../../../../resources/views/filament/**/*';
@source '../../../../app/Filament';
@source '../../../../resources/views/filament';
@source '../../../../resources/views/livewire/**/*.blade.php';

/* Arabic Fonts for Quran Text */
@font-face {
    font-family: 'UthmanicQaloun';
    src: url('../../../../public/fonts/UthmanicQaloun V21.ttf') format('truetype'),
         url('../../../../public/fonts/uthmanic_qaloun_v21.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'qaloon-v8';
    src: url('../../../../public/fonts/uthmanic_hafs_v22.ttf') format('truetype'),
         url('../../../../public/fonts/qaloon-v8-full-org.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Base Arabic text styling */
.arabic-verse-text {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    font-size: 18px !important;
    line-height: 1.8 !important;
    text-align: right !important;
    direction: rtl !important;
    color: #1e1e1e;
    font-weight: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Table column specific styling */
.fi-ta-text .arabic-verse-text {
    font-size: 16px !important;
    line-height: 1.6 !important;
    padding: 4px 8px;
    width: 100%;
    display: block;
}

/* Select dropdown options styling */
.arabic-select-wrapper .fi-select-input {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    font-size: 16px !important;
    text-align: right !important;
    direction: rtl !important;
}

/* Choices.js dropdown styling for Arabic */
.arabic-select-wrapper .choices__list--dropdown .choices__item {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    text-align: right !important;
    direction: rtl !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    padding: 8px 12px !important;
}

.arabic-select-wrapper .choices__input {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    text-align: right !important;
    direction: rtl !important;
}

/* Dark mode support */
:root.dark .arabic-verse-text {
    color: #e5e5e5;
}

/* Responsive font sizes */
@media (max-width: 768px) {
    .arabic-verse-text {
        font-size: 16px !important;
        line-height: 1.7 !important;
    }

    .fi-ta-text .arabic-verse-text {
        font-size: 14px !important;
        line-height: 1.5 !important;
    }
}

/* Additional styling for better Arabic text rendering */
.arabic-select-wrapper select,
.arabic-select-wrapper .choices__inner,
.arabic-select-wrapper .choices__list--single,
.arabic-select-wrapper .choices__item--selectable {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    text-align: right !important;
    direction: rtl !important;
}

/* Ensure proper styling for Filament's Select component */
.arabic-select-wrapper .fi-select-input,
.arabic-select-wrapper .fi-select-input option {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    text-align: right !important;
    direction: rtl !important;
    font-size: 16px !important;
}

/* Style for the selected value display */
.arabic-select-wrapper .fi-select-input:not([multiple]) {
    text-align: right !important;
    direction: rtl !important;
}

/* Ensure dropdown options are properly styled */
.arabic-select-wrapper option {
    font-family: 'UthmanicQaloun', 'qaloon-v8', serif !important;
    text-align: right !important;
    direction: rtl !important;
    padding: 8px 12px !important;
}
